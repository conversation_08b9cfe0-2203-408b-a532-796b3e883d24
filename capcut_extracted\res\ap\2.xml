<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools"
  tools:shrinkMode="strict"
  tools:keep="@layout/abc*,
  @drawable/icon,
  @string/cc_keep,
  @string/com.crashlytics.*,
  @string/google_app_id,
  @string/gcm_defaultSenderId,
  @string/default_web_client_id,
  @string/ga_trackingId,
  @string/firebase_database_url,
  @string/google_api_key,
  @string/google_crash_reporting_api_key,
  @anim/umeng*,
  @string/umeng*,
  @string/UM*,
  @string/tb_*,
  @layout/umeng*,
  @layout/socialize_*,
  @layout/*messager*,
  @layout/tb_*,
  @color/umeng*,
  @color/tb_*,
  @style/*UM*,
  @style/umeng*,
  @drawable/umeng*,
  @drawable/tb_*,
  @drawable/qq_*,
  @drawable/tb_*,
  @id/umeng*,
  @id/*messager*,
  @id/progress_bar_parent,
  @id/socialize_*,
  @id/webView,
  @string/google_app_id,
  @string/gcm_defaultSenderId,
  @string/default_web_client_id,
  @string/ga_trackingId,
  @string/firebase_database_url,
  @string/google_api_key,
  @string/google_crash_reporting_api_key,
  @drawable/push,
  @drawable/push_small,
  @layout/getui_notification,
  @drawable/jpush_notification_icon,
  @string/growingio_project_id,
  @string/growingio_url_scheme,
  @string/growingio_channel,
  @string/hms_*,
  @string/connect_server_fail_prompt_toast,
  @string/getting_message_fail_prompt_toast,
  @string/no_available_network_prompt_toast,
  @string/third_app_*,
  @string/upsdk_*,
  @style/upsdkDlDialog,
  @style/AppTheme,
  @style/AppBaseTheme,
  @styleable/TextAppearance,
  @dimen/upsdk_dialog_*,
  @color/upsdk_*,
  @layout/upsdk_*,
  @drawable/upsdk_*,
  @drawable/hms_*,
  @layout/hms_*,
  @id/hms_*,
  @bool/com.crashlytics.useFirebaseAppId,
  @string/com.crashlytics.useFirebaseAppId,
  @string/google_app_id,
  @bool/com.crashlytics.CollectDeviceIdentifiers,
  @string/com.crashlytics.CollectDeviceIdentifiers,
  @bool/com.crashlytics.CollectUserIdentifiers,
  @string/com.crashlytics.CollectUserIdentifiers,
  @string/com.crashlytics.ApiEndpoint,
  @string/io.fabric.android.build_id,
  @string/com.crashlytics.android.build_id,
  @bool/com.crashlytics.RequireBuildId,
  @string/com.crashlytics.RequireBuildId,
  @bool/com.crashlytics.CollectCustomLogs,
  @string/com.crashlytics.CollectCustomLogs,
  @bool/com.crashlytics.Trace,
  @string/com.crashlytics.Trace,
  @string/com.crashlytics.CollectCustomKeys,
  @id/ssdk*,
  @string/mobcommon*,
  @string/ssdk*,
  @string/mobdemo*,
  @drawable/mobcommon*,
  @drawable/ssdk*,
  @layout/mob*,
  @style/mobcommon*,
  @string/tt_*,
  @integer/tt_*,
  @layout/tt_*,
  @drawable/tt_*,
  @style/tt_*,
  @dimen/tt_*,
  @anim/tt_*,
  @color/tt_*,
  @id/tt_*,
  @drawable/after_heat_tips,
  @string/umeng*,
  @string/UM*,
  @string/tb_*,
  @layout/umeng*,
  @layout/tb_*,
  @drawable/umeng*,
  @drawable/tb_*,
  @anim/umeng*,
  @color/umeng*,
  @color/tb_*,
  @style/*UM*,
  @style/umeng*,
  @id/umeng*,
  @string/google_app_id,
  @string/gcm_defaultSenderId,
  @string/default_web_client_id,
  @string/ga_trackingId,
  @string/firebase_database_url,
  @string/google_api_key,
  @string/google_crash_reporting_api_key,
  @dimen/navigation_bar_height,
  @bool/config_showNavigationBar,
  @drawable/src_res*,
  @drawable/bg_rectangular_button_grey,
  @drawable/splash_activity_banner,
  @string/CODE_PUSH_APK_BUILD_TIME,
  @dimen/navigation_bar_height,
  @dimen/status_bar_height,
  @bool/config_showNavigationBar,
  @raw/*,
  @drawable/mz_push_notification_small_icon,
  @layout/notification_text_43,
  @layout/notification_text_34,
  @layout/notification_text,
  @id/title,
  @id/text,
  @id/time,
  @drawable/tt_appdownloader_*,
  @layout/tt_appdownloader_*,
  @string/tt_appdownloader_*,
  @color/tt_appdownloader_*,
  @style/tt_appdownloader_*,
  @id/tt_appdownloader_*,
  @id/iv_play,
  @id/tv_pop_time_current,
  @id/view_progress_slash,
  @id/tv_pop_time_total,
  @id/tv_speed,
  @id/longer_video_feed_item_comment_action,
  @id/video_digg_view,
  @id/longer_video_landscape_more,
  @id/longer_video_landscape_close,
  @id/longer_video_landscape_user_layout,
  @id/longer_video_landscape_xigua_tips,
  @layout/fragment_main_page,
  @layout/fragment_main,
  @layout/activity_main,
  @layout/fragment_feed,
  @layout/item_feed,
  @layout/layout_feed_avatar,
  @layout/addiction_hint,
  @layout/layout_video_share,
  @layout/layout_video_comment_count,
  @layout/layout_video_desc,
  @layout/layout_video_digg,
  @layout/layout_video_tag,
  @layout/view_video_mix_bar,
  @layout/view_video_hot_search_bar,
  @layout/layout_video_cover_music,
  @layout/layout_video_title_music,
  @layout/view_video_progress_bar,
  @layout/layout_video_post_time,
  @layout/fragment_main,
  @layout/fragment_friend_tab,
  @layout/item_feed_image,
  @layout/layout_main_tab_text,
  @layout/homepage_common_view_main_tab_strip,
  @layout/layout_main_tab_avatar,
  @layout/common_feed_bottom_view_layout,
  @layout/nearby_fragment_timeline,
  @drawable/status_icon,
  @drawable/status_icon_l,
  @drawable/mz_push_notification_small_icon,
  @drawable/hwpush*,
  @layout/hwpush*,
  @menu/hwpush*,
  @string/hms*,
  @string/upsdk_*,
  @drawable/upsdk_*,
  @string/connect_server_fail_prompt_toast,
  @string/getting_message_fail_prompt_toast,
  @string/no_available_network_prompt_toast,
  @string/third_app_*,
  @layout/hms*,
  @layout/upsdk_*,
  @id/*,
  @drawable/upsdk*,
  @color/upsdk*,
  @dimen/upsdk*,
  @style/upsdk*,
  @string/agc*,
  @color/emui*,
  @color/hwpush*,
  @color/upsdk*,
  @dimen/emui*,
  @dimen/margin_l,
  @dimen/margin_m,
  @dimen/margin_xs,
  @dimen/upsdk_dialog*,
  @drawable/hms*,
  @drawable/jpush_notification_icon,
  @drawable/push,
  @drawable/push_small,
  @drawable/qq_*,
  @drawable/stat_sys_third_app_notify,
  @id/*messager*,
  @id/action,
  @id/big_pic,
  @id/cancel_bg,
  @id/cancel_imageview,
  @id/divider,
  @id/download_info_progress,
  @id/hms*,
  @id/hwpush*,
  @id/icon,
  @id/line1,
  @id/line3,
  @id/linear_buttons,
  @id/linear_icons,
  @id/listview_layout,
  @id/name_layout,
  @id/name_textview,
  @id/progress_bar_parent,
  @id/right_btn,
  @id/scroll_layout,
  @id/small_btn,
  @id/smallicon,
  @id/socialize_*,
  @id/status_bar_latest_event_content,
  @id/third_app_dl_progress_text,
  @id/third_app_dl_progressbar,
  @id/third_app_warn_text,
  @id/webView,
  @layout/*messager*,
  @layout/getui_notification,
  @layout/socialize_*,
  @layout/upsdk*,
  @plurals/hwpush*,
  @string/cloudpush_app_name,
  @string/cloudpush_version_value,
  @string/hwpush*,
  @string/project_id,
  @string/upsdk*,
  @attr/panelMenuListWidth,
  @id/one_key_login,
  @id/one_key_login_*,
  @id/multi_accounts_item_*,
  @string/bd_open*,
  @id/bd_open*,
  @layout/tt_bd_open*,
  @layout/tt_open*,
  @anim/tt_*,
  @color/tt_*,
  @dimen/tt_*,
  @drawable/tt_*,
  @id/tag_ignore,
  @id/tag_view_name,
  @id/tt_*,
  @id/web_frame,
  @integer/tt_video_progress_max,
  @layout/tt_*,
  @string/app_name,
  @string/tt_*,
  @style/DialogFullscreen,
  @style/EditTextStyle,
  @style/Theme_Dialog_TTDownload,
  @style/Theme_Dialog_TTDownloadOld,
  @style/tt_*,
  @raw/tt_*,
  @mipmap/tt_*,
  @menu/tt_*,
  @attr/tt_*,
  @integer/tt_*,
  @string/beauty_portrait,
  @string/cut_out_n,
  @string/brush_n,
  @string/sticker_s_n,
  @string/adjust_n,
  @string/jianying_home_aidub_pagetitle,
  @string/jianying_home_aidub_input_info,
  @string/import_editing,
  @string/ai_filters,
  @string/edit_cover,
  @string/mbridge*,
  @layout/mbridge*,
  @drawable/mbridge*,
  @anim/mbridge*,
  @color/mbridge*,
  @style/mbridge*,
  @id/mbridge*,
  @drawable/applovin*,
  @anim/gab_*,
  @color/gab_*,
  @dimen/gab_*,
  @drawable/gab_*,
  @id/gab_*,
  @layout/gab_*,
  @string/gab_*,
  @style/gab_*,@attr/layout_goneMarginBottom,
  @attr/layout_goneMarginRight,
  @attr/layout_goneMarginLeft,
  @attr/layout_goneMarginTop,
  @attr/roundBottomRight,
  @attr/layout_constraintHeight_percent,
  @attr/layout_constraintHorizontal_bias,
  @attr/paddingLeftSystemWindowInsets,
  @attr/paddingRightSystemWindowInsets,
  @attr/layout_constraintBottom_toBottomOf,
  @attr/retryImageScaleType,
  @attr/paddingTopSystemWindowInsets,
  @attr/layout_constraintBottom_toTopOf,
  @attr/roundTopLeft,
  @attr/layout_constraintBaseline_toBaselineOf,
  @attr/roundBottomEnd,
  @attr/layout_constraintGuide_begin,
  @attr/layout_constraintHeight_max,
  @attr/roundAsCircle,
  @attr/layout_behavior,
  @attr/placeholderImageScaleType,
  @attr/layout_constraintHorizontal_chainStyle,
  @attr/layout_constrainedWidth,
  @attr/placeholderImage,
  @attr/layout_constraintRight_toLeftOf,
  @attr/layout_constraintLeft_toLeftOf,
  @attr/gestureInsetBottomIgnored,
  @attr/layout_constraintGuide_percent,
  @attr/roundBottomLeft,
  @attr/roundedCornerRadius,
  @attr/shapeAppearanceOverlay,
  @attr/layout_constraintTop_toBottomOf,
  @attr/layout_constraintLeft_toRightOf,
  @attr/selectableItemBackground,
  @attr/layout_constraintEnd_toStartOf,
  @attr/layout_constraintEnd_toEndOf,
  @attr/layout_constraintVertical_chainStyle,
  @attr/layout_constraintWidth_default,
  @attr/roundingBorderColor,
  @attr/layout_constraintVertical_bias,
  @attr/layout_constraintWidth_percent,
  @attr/layout_constraintStart_toEndOf,
  @attr/roundingBorderWidth,
  @attr/layout_constraintStart_toStartOf,
  @attr/layout_constraintTop_toTopOf,
  @attr/colorControlActivated,
  @attr/showText,
  @attr/colorControlNormal,
  @attr/actualImageResource,
  @attr/actualImageScaleType,
  @attr/colorPrimary,
  @attr/colorPrimaryDark,
  @attr/colorAccent,
  @attr/contentInsetStart,
  @attr/bottomSheetStyle,
  @attr/elevation,
  @attr/layout_constraintRight_toRightOf,
  @attr/shapeAppearance,
  @attr/contentInsetEnd,
  @attr/track,
  @attr/backgroundTint,
  @attr/behavior_peekHeight,
  @attr/behavior_halfExpandedRatio,
  @attr/srcCompat,
  @attr/switchMinWidth,
  @attr/behavior_hideable,
  @attr/behavior_saveFlags,
  @attr/spanCount,
  @attr/behavior_fitToContents,
  @attr/tabSelectedTextColor,
  @attr/tabRippleColor,
  @attr/behavior_expandedOffset,
  @attr/behavior_draggable,
  @attr/layout_scrollFlags,
  @attr/overlayImage,
  @attr/failureImageScaleType,
  @attr/fadeDuration,
  @attr/failureImage,
  @attr/paddingBottomSystemWindowInsets,
  @attr/trackTint,
  @attr/tabBackground,
  @attr/behavior_skipCollapsed,
  @attr/tabGravity,
  @attr/tabIndicatorColor,
  @attr/switchStyle,
  @attr/tabIndicatorHeight,
  @attr/tabIndicatorFullWidth,
  @attr/viewAspectRatio,
  @color/accent_material_light,
  @dimen/design_bottom_sheet_peek_height_min,
  @style/Theme.AppCompat.Light.DarkActionBar,
  @style/Theme.AppCompat.Dialog,
  @style/Theme.AppCompat.Light.Dialog.Alert,
  @style/Theme.AppCompat.Light.NoActionBar,
  @style/Theme.AppCompat.Light.Dialog,
  @style/Base.Widget.AppCompat.CompoundButton.CheckBox,
  @style/AppCompatNoActionBarTheme,
  @style/Widget.AppCompat.ProgressBar,
  @style/Widget.AppCompat.ProgressBar.Horizontal,
  @style/Widget.AppCompat.Button.Borderless.Colored,
  @style/Widget.AppCompat.CompoundButton.CheckBox,
  @style/Widget.AppCompat.CompoundButton.RadioButton,
  @style/Widget.AppCompat.CompoundButton.Switch,
  @anim/cj_pay_activity_add_in_animation,
  @anim/cj_pay_activity_remove_out_animation,
  @drawable/cj_pay_bg_payment_icon_unable,
  @drawable/cj_pay_bg_check_box,
  @drawable/cj_pay_bg_payment_method_recommend_way,
  @string/cj_pay_recommend_way,
  @string/follow_success,
  @attr/layout_constraintHorizontal_weight,
  @attr/layout_constrainedHeight,
  @color/cj_pay_color_gray_202,
  @color/cj_pay_color_gray_153,
  @color/cj_pay_color_red,
  @color/cj_pay_color_gray_232,
  @color/cj_pay_color_white,
  @color/cj_pay_color_trans,
  @color/cj_pay_color_black_64,
  @dimen/cj_pay_height_24,
  @dimen/cj_pay_font_size_10,
  @dimen/cj_pay_font_size_12,
  @dimen/cj_pay_font_size_16,
  @dimen/cj_pay_padding_8,
  @dimen/cj_pay_padding_4,
  @dimen/cj_pay_padding_5,
  @dimen/cj_pay_padding_24,
  @dimen/cj_pay_padding_16,
  @dimen/cj_pay_height_half_1,
  @layout/cj_pay_view_titlebar_layout,
  @style/CJ_Pay_Translucent,
  @string/enter_douyin_live,
  @string/current_version_un_support,
  @attr/flow_wrapMode,
  @attr/flow_verticalBias,
  @attr/flow_verticalAlign,
  @attr/flow_verticalStyle,
  @attr/flow_verticalGap,
  @attr/transitionEasing,
  @attr/transitionPathRotate,
  @attr/flow_firstVerticalBias,
  @attr/flow_firstHorizontalStyle,
  @attr/flow_firstHorizontalBias,
  @attr/flow_lastVerticalBias,
  @attr/barrierMargin,
  @attr/flow_lastHorizontalStyle,
  @attr/flow_maxElementsWrap,
  @attr/flow_lastVerticalStyle,
  @attr/flow_horizontalGap,
  @attr/flow_horizontalBias,
  @attr/barrierDirection,
  @attr/flow_lastHorizontalBias,
  @attr/barrierAllowsGoneWidgets,
  @attr/flow_horizontalStyle,
  @attr/flow_horizontalAlign,
  @attr/flow_firstVerticalStyle,
  @attr/chainUseRtl,
  @attr/constraint_referenced_tags,
  @attr/constraint_referenced_ids,
  @attr/animate_relativeTo,
  @attr/motionStagger,
  @attr/motionProgress,
  @attr/layout_constraintTop_creator,
  @attr/layout_constraintTag,
  @attr/drawPath,
  @attr/layout_constraintWidth_max,
  @attr/layout_constraintVertical_weight,
  @attr/layout_constraintWidth_min,
  @attr/layout_constraintLeft_creator,
  @attr/layout_constraintHeight_min,
  @attr/layout_constraintRight_creator,
  @attr/layout_constraintDimensionRatio,
  @attr/layout_constraintCircleRadius,
  @attr/layout_constraintHeight_default,
  @attr/deriveConstraintsFrom,
  @attr/layout_constraintGuide_end,
  @attr/layout_editor_absoluteY,
  @attr/layout_editor_absoluteX,
  @attr/layout_constraintBaseline_creator,
  @attr/layout_constraintBottom_creator,
  @attr/layout_constraintCircleAngle,
  @attr/layout_constraintCircle,
  @attr/pivotAnchor,
  @attr/pathMotionArc,
  @drawable/cj_pay_icon_titlebar_left_close,
  @drawable/cj_pay_icon_titlebar_left_arrow,
  @drawable/cj_pay_bind_card_safe_light_bg,
  @string/cj_pay_h5_result_illegal_params,
  @string/cj_pay_face_check,
  @string/cj_pay_common_dialog_confirm,
  @string/cj_pay_common_dialog_cancel,
  @string/cj_pay_quick_bind_h5_title,
  @string/cj_pay_permission_storage_title,
  @attr/cj_pay_page_bg_color,
  @attr/layout_anchorGravity,
  @attr/layout_anchor,
  @attr/windowNoTitle,
  @attr/windowActionBar,
  @attr/cardElevation,
  @attr/cardCornerRadius,
  @attr/cardBackgroundColor,
  @attr/roundTopStart,
  @attr/roundTopEnd,
  @color/cj_pay_color_black_34,
  @color/cj_pay_color_new_blue,
  @layout/cj_pay_view_quickbind_titlebar_layout,
  @layout/cj_pay_view_network_error_layout,
  @style/Theme.AppCompat.NoActionBar,
  @style/TextAppearance.AppCompat.Headline,
  @style/TextAppearance.AppCompat.Subhead,
  @style/TextAppearance.AppCompat.Caption,
  @style/Theme.AppCompat.Light,
  @attr/cardUseCompatPadding,
  @attr/cardPreventCornerOverlap,
  @anim/shopping_popup_fade_out,
  @anim/shopping_popup_fade_in,
  @anim/ec_commerce_pre_out,
  @anim/ec_commerce_activity_out,
  @anim/ec_commerce_activity_in,
  @attr/trackTintMode,
  @attr/splitTrack,
  @attr/autoSizeTextType,
  @attr/autoSizeMaxTextSize,
  @attr/autoSizeMinTextSize,
  @attr/editTextStyle,
  @attr/thumbTintMode,
  @attr/thumbTextPadding,
  @attr/tabPaddingStart,
  @attr/tabPadding,
  @attr/tabMode,
  @attr/tabPaddingEnd,
  @attr/tabPaddingBottom,
  @attr/tabMinWidth,
  @attr/tabPaddingTop,
  @attr/tabUnboundedRipple,
  @attr/tabStyle,
  @attr/contentPaddingTop,
  @attr/tabIndicatorGravity,
  @attr/contentPaddingLeft,
  @attr/bottomSheetDialogTheme,
  @attr/tabIndicatorAnimationMode,
  @attr/tabMaxWidth,
  @attr/tabInlineLabel,
  @attr/contentPaddingRight,
  @attr/tabIconTintMode,
  @attr/tabIconTint,
  @attr/tabIndicatorAnimationDuration,
  @attr/contentPaddingBottom,
  @attr/tabIndicator,
  @attr/tabContentStart,
  @attr/fontFamily,
  @attr/switchPadding,
  @attr/switchTextAppearance,
  @attr/statusBarBackground,
  @attr/layout_collapseMode,
  @color/ec_store_window_background,
  @dimen/design_tab_scrollable_min_width,
  @dimen/design_tab_text_size_2line,
  @layout/design_layout_tab_text,
  @layout/design_layout_tab_icon,
  @style/Theme.MaterialComponents.Light,
  @style/ThemeOverlay.AppCompat.Dialog,
  @style/PopupWindowFadeAnimationStyle,
  @style/Theme.AppCompat,
  @style/Theme.AppCompat.Dialog.Alert,
  @style/ECHalfScreenAnchorV4Anime,
  @style/ECSlideInWindowAnimation,
  @style/ECBottomInWindowAnimation,
  @style/ECBottomOutWindowAnimation,
  @style/ECBaseDialogFragmentAnimation,
  @style/Widget.MaterialComponents.BottomSheet.Modal,
  @style/ECSlideOutWindowAnimation,
  @style/TextAppearance.Design.Tab,
  @style/Theme.Design.Light.BottomSheetDialog,
  @style/Theme.Design.BottomSheetDialog,
  @style/StoreAppBottomSheetStyle,
  @style/commerce_dialog_dim_non_enter_animation,
  @style/ec_sku_prerender_dialog_anim,
  @style/bottom_sheet_anime,
  @style/Widget.Design.BottomSheet.Modal,
  @style/Dialog.BottomSheet.Transparent,
  @style/Animation.Design.BottomSheetDialog,
  @style/Widget.Design.TabLayout,
  @style/TextAppearance.AppCompat,
  @id/design_bottom_sheet,
  @id/touch_outside,
  @attr/lottie_loop,
  @attr/lottie_imageAssetsFolder,
  @attr/lottie_fileName,
  @attr/lottie_autoPlay,
  @string/byted_cancle,
  @drawable/ec_dux_ic_s_s_arrowright_outlined_16,
  @drawable/ec_dux_ic_s_s_checkmark_outlined_16,
  @drawable/ec_dux_ic_s_s_checkmark_outlined,
  @drawable/ec_dux_ic_s_s_xmark_outlined,
  @drawable/ec_dux_ic_s_s_arrowleft_outlined,
  @drawable/commerce_ic_playback_card_close_btn,
  @drawable/commerce_ic_playback_card_arrow_red,
  @attr/icon_size,
  @attr/bg_strokeColor,
  @attr/bg_strokeWidth,
  @attr/bg_startColor,
  @attr/bg_cornerRadius_BL,
  @attr/bg_cornerRadius_TL,
  @attr/bg_cornerRadius_BR,
  @attr/bg_endColor,
  @attr/bg_cornerRadius_TR,
  @attr/disableClick,
  @attr/showLeftIcon,
  @attr/leftIconRes,
  @attr/showRightText,
  @attr/checkBoxSize,
  @attr/icon_end_size,
  @attr/icon_end_color,
  @attr/icon_color,
  @attr/bg_color,
  @attr/bg_cornerRadius,
  @attr/autoPlayAnimation,
  @attr/always_clickable,
  @attr/layoutMode,
  @attr/rightText,
  @color/white_75,
  @color/white_90,
  @color/white_0,
  @color/ec_commerce_color_292929_34,
  @color/ec_commerce_primary,
  @color/ec_color_black_34,
  @color/ec_dux_black_161823,
  @color/ec_dux_black_161823_34,
  @style/ECButton.Rectangle.Primary.Large,
  @drawable/stub_ic_launcher,
  @string/stub_app_name,
  @drawable/abc_btn_radio_to_on_mtrl_000,
  @drawable/abc_btn_radio_to_on_mtrl_015,
  @string/appbar_scrolling_view_behavior,
  @attr/layoutManager,
  @attr/roundTopRight,
  @attr/thumbTint,
  @attr/tabTextColor,
  @attr/tabTextAppearance,
  @attr/layout_goneMarginStart,
  @attr/layout_goneMarginEnd,
  @string/afghanistan,
  @string/albania,
  @string/algeria,
  @string/angola,
  @string/anguilla,
  @string/antigua_and_barbuda,
  @string/argentina,
  @string/armenia,
  @string/australia,
  @string/austria,
  @string/azerbaijan,
  @string/bahamas,
  @string/bahrain,
  @string/barbados,
  @string/belarus,
  @string/belgium,
  @string/belize,
  @string/bermuda,
  @string/bhutan,
  @string/bolivia,
  @string/bosnia_and_herzegovina,
  @string/botswana,
  @string/brazil,
  @string/british_virgin_islands,
  @string/brunei,
  @string/bulgaria,
  @string/burkina_faso,
  @string/cameroon,
  @string/canada,
  @string/cape_verde,
  @string/cayman_islands,
  @string/chad,
  @string/china,
  @string/congo_democratic_republic_of,
  @string/congo_republic_of_the,
  @string/cote_d_ivoire,
  @string/croatia,
  @string/cyprus,
  @string/czech_republic,
  @string/denmark,
  @string/dominica,
  @string/dominican_republic,
  @string/el_salvador,
  @string/ecuador,
  @string/egypt,
  @string/estonia,
  @string/fiji,
  @string/finland,
  @string/france,
  @string/gabon,
  @string/gambia,
  @string/georgia_state,
  @string/germany,
  @string/ghana,
  @string/greece,
  @string/grenada,
  @string/guatemala,
  @string/guinea_bissau,
  @string/guyana,
  @string/honduras,
  @string/hongkong,
  @string/hungary,
  @string/iceland,
  @string/india,
  @string/indonesia,
  @string/iraq,
  @string/ireland,
  @string/israel,
  @string/italy,
  @string/jamaica,
  @string/japan,
  @string/jordan,
  @string/cambodia,
  @string/kazakhstan,
  @string/kosovo,
  @string/kuwait,
  @string/kyrgyzstan,
  @string/laos,
  @string/latvia,
  @string/lebanon,
  @string/liberia,
  @string/libya,
  @string/lithuania,
  @string/macao,
  @string/madagascar,
  @string/malawi,
  @string/malaysia,
  @string/maldives,
  @string/mali,
  @string/malta,
  @string/mauritania,
  @string/mauritius,
  @string/mexico,
  @string/moldova,
  @string/mongolia,
  @string/montenegro,
  @string/montserrat,
  @string/morocco,
  @string/myanmar,
  @string/namibia,
  @string/nauru,
  @string/nepal,
  @string/netherlands,
  @string/new_zealand,
  @string/nicaragua,
  @string/niger,
  @string/nigeria,
  @string/macedonia,
  @string/norway,
  @string/oman,
  @string/pakistan,
  @string/palau,
  @string/panama,
  @string/papua_new_guinea,
  @string/paraguay,
  @string/peru,
  @string/philippines,
  @string/poland,
  @string/portugal,
  @string/qatar,
  @string/romania,
  @string/russia,
  @string/rwanda,
  @string/republic_of_korea,
  @string/sao_tome_and_principe,
  @string/saudi_arabia,
  @string/senegal,
  @string/serbia,
  @string/seychelles,
  @string/sierra_leone,
  @string/singapore,
  @string/slovakia,
  @string/slovenia,
  @string/solomon_islands,
  @string/south_africa,
  @string/spain,
  @string/sri_lanka,
  @string/st_lucia,
  @string/st_vincent_and_grenadines,
  @string/swaziland,
  @string/sweden,
  @string/taiwan,
  @string/tajikistan,
  @string/tanzania,
  @string/thailand,
  @string/tonga,
  @string/trinidad_and_tobago,
  @string/tunisia,
  @string/turkey,
  @string/turkmenistan,
  @string/turks_and_caicos_islands,
  @string/uganda,
  @string/ukraine,
  @string/united_arab_emirates,
  @string/united_kingdom,
  @string/united_states,
  @string/uruguay,
  @string/uzbekistan,
  @string/vanuatu,
  @string/venezuela,
  @string/vietnam,
  @string/yemen,
  @string/zambia,
  @string/zimbabwe,
  @string/jianying_home_aidub_pagetitle,
  @string/jianying_home_aidub_input_info,
  @string/mob_m10n_compare_plans_title,
  @string/mob_m10n_compare_plans_subtitle_benefits,
  @string/mob_m10n_compare_plans_subtitle_standard,
  @string/mob_m10n_compare_plans_subtitle_pro,
  @string/mob_m10n_compare_plans_subtitle_teams,
  @string/mob_m10n_compare_plans_subtitle_teams_per_seat,
  @string/mob_smart_vlog_see_more_button,
  @string/mob_aigc_credit_check_balance_ai_points,
  @string/free_trial_period,
  @string/mob_me_sign_in_for_deals,
  @string/free_subscribe_icon,
  @string/purchase_now,
  @string/next_period_charge,
  @string/open_page_subtitle_free_trial,
  @string/mob_m10n_subscribe_benefit_mobile_only,
  @string/mob_m10n_subscribe_benefit_all_platforms,
  @string/mob_m10n_compare_plans_collapse_btn,
  @string/mob_m10n_compare_plans_expand_btn,
  @string/mob_m10n_subscribe_tab_standard,
  @string/plus_vip,
  @string/mob_m10n_teams,
  @string/tap_on_button,
  @string/tap_on_purchase,
  @string/privacy_policy,
  @string/terms_of_service,
  @string/mob_m10n_new_pro_subscription_subtitle,
  @string/mob_m10n_standard_renewal_valid_upgrade_btn,
  @string/mob_m10n_plan_renew_btn,
  @string/mob_m10n_subscription_auto_renew_info_standard,
  @string/mob_m10n_subscription_auto_renew_info_pro,
  @string/mob_m10n_subscription_expire_info_standard,
  @string/mob_m10n_subscription_expire_info_pro,
  @string/mob_m10n_subscription_card_credits,
  @string/mob_m10n_subscription_card_cloud_space,
  @string/mob_m10n_subscription_manage_btn,
  @string/after_comfirm_purchase_renew_android,
  @string/mob_m10n_teams_legal_statement_google_play,
  @string/mob_m10n_teams_legal_price,
  @string/subscription_question,
  @string/mob_m10n_seats_option,
  @string/mob_m10n_seats_price_start,
  @string/mob_m10n_choose_space_to_upgrade,
  @string/mob_m10n_title_choose_subscribe_seat,
  @string/mob_m10n_space_current_member,
  @string/mob_m10n_seats_cannot_select,
  @string/crossed_line_ref,
  @string/mob_m10n_banner_using_pro_features,
  @string/mob_m10n_export_paywall_popup_title,
  @string/mob_m10n_export_paywall_popup_text,
  @string/mob_join_svip_credits_button,
  @string/not_now_buy,
  @string/m10n_payment_error_title_couldnot_pay,
  @string/mob_pay_there_many_many_errors_reflect,
  @string/m10n_payment_error_btn_ok,
  @string/psr_upsell_word_1,
  @string/mob_m10n_about_subscriptions_title,
  @string/mob_m10n_about_subscriptions_desc_1,
  @string/mob_m10n_about_subscriptions_desc_2,
  @string/mob_m10n_about_subscriptions_btn,
  @string/mob_m10n_purchase_not_show_tip,
  @string/mob_m10n_feature_display_btn_try,
  @string/mob_m10n_toast_team_upgrade_success,
  @string/expire_on_date,
  @string/renews_on,
  @string/mob_m10n_button_renew,
  @string/mob_m10n_title_benefits_usage,
  @string/mob_m10n_title_benefits_storage,
  @string/mob_m10n_button_expand_space,
  @string/mob_m10n_title_seats_usage,
  @string/mob_m10n_seats_details_view,
  @string/mob_m10n_seats_details_usage,
  @string/mob_m10n_button_upgrade_seats,
  @string/mob_aigc_credit_balance_ai_features,
  @plurals/mob_m10n_price_desc_month,
  @plurals/mob_m10n_price_desc_year,
  @string/mob_m10n_title_seat_upgrade_max,
  @string/mob_m10n_info_seat_upgrade_max,
  @string/mob_m10n_button_seat_upgrade_contact,
  @string/mob_m10n_button_seat_upgrade_contact_later,
  @string/mob_m10n_title_access_suspended,
  @string/mob_m10n_desc_reduce_member_number,
  @string/mob_m10n_btn_remove_member,
  @string/mob_m10n_btn_not_now,
  @string/mob_m10n_title_contact_owner_renew,
  @string/mob_m10n_desc_contact_owner_renew,
  @string/mob_m10n_button_got_it,
  @string/m10n_checkoutrecall_freetrailday,
  @string/m10n_placeholderonly_currencyamount,
  @string/m10n_placeholderonly_percentage,
  @string/m10n_draw_noCash_h5_new3,
  @string/m10n_draw_noCash_h5_new4,
  @string/m10n_switch_plan_desc,
  @string/m10n_switch_plan_popup_title,
  @string/m10n_switch_plan_popup_cancel_btn,
  @string/m10n_switch_plan_popup_confirm_btn,
  @string/network_error,
  @string/mob_m10n_checkout_about_subscriptions_title,
  @string/mob_m10n_checkout_about_subscriptions_btn,
  @string/cc_mob_m10n_sku_weekly_popup_title,
  @string/cc_mob_teams_expire_date,
  @string/cc_mob_teams_renew_date,
  @string/cc_mob_teams_seats_option,
  @string/cc_mob_m10n_sku_weekly_tag,
  @string/mob_visitor_vip_login_now,
  @string/mob_visitor_vip_login_purchase_benefit,
  @string/mob_visitor_vip_login,
  @string/mob_visitor_vip_visitor_mode,
  @string/mob_visitor_vip_title_sign_up,
  @string/mob_visitor_vip_signin_one,
  @string/mob_visitor_vip_btn_sign_up,
  @plurals/open_page_desc_free_trial_renew_price_client,
  @plurals/open_page_desc_free_trial_renew_price_year_client,
  @plurals/mob_m10n_price_desc_month_client,
  @plurals/mob_m10n_price_desc_year_client,
  @string/ccug_membership_x_days,
  @string/mob_m10n_title_team_benefits_details,
  @string/click_to_open,
  @plurals/mob_m10n_space_current_member_client,
  @plurals/cc_mob_m10n_sku_weekly_btn_tag_renew_client,
  @string/cc_mob_m10n_sku_weekly_btn_tag_ft,
  @string/mob_m10n_sku_price_first_month,
  @string/mob_m10n_sku_price_first_week,
  @plurals/mob_m10n_subcribe_btn_ft_start_client,
  @string/cc_mob_m10n_checkout_sku_yearly_price"
  />