#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import zipfile
import xml.etree.ElementTree as ET
from xml.dom import minidom

def read_binary_xml(file_path):
    """محاولة قراءة ملف XML الثنائي"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # طباعة أول 200 بايت لفهم البنية
        print("أول 200 بايت من AndroidManifest.xml:")
        print(content[:200])
        print("\n" + "="*50 + "\n")
        
        # محاولة البحث عن نصوص قابلة للقراءة
        readable_strings = []
        current_string = ""
        
        for byte in content:
            if 32 <= byte <= 126:  # ASCII printable characters
                current_string += chr(byte)
            else:
                if len(current_string) > 3:  # سلاسل نصية أطول من 3 أحرف
                    readable_strings.append(current_string)
                current_string = ""
        
        if len(current_string) > 3:
            readable_strings.append(current_string)
        
        print("النصوص القابلة للقراءة في AndroidManifest.xml:")
        for i, string in enumerate(readable_strings[:50]):  # أول 50 نص
            print(f"{i+1}: {string}")
            
        return readable_strings
        
    except Exception as e:
        print(f"خطأ في قراءة الملف: {e}")
        return []

def analyze_apk_structure():
    """تحليل هيكل APK"""
    apk_path = "capcut-14-7-0.apk"
    
    if not os.path.exists(apk_path):
        print(f"ملف APK غير موجود: {apk_path}")
        return
    
    print("تحليل هيكل APK...")
    
    try:
        with zipfile.ZipFile(apk_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            # البحث عن ملفات مهمة
            manifest_files = [f for f in file_list if 'manifest' in f.lower()]
            activity_files = [f for f in file_list if 'activity' in f.lower()]
            splash_files = [f for f in file_list if any(keyword in f.lower() for keyword in ['splash', 'launch', 'start', 'welcome'])]
            
            print(f"إجمالي الملفات في APK: {len(file_list)}")
            print(f"\nملفات Manifest: {manifest_files}")
            print(f"ملفات Activity: {activity_files[:10]}...")  # أول 10 ملفات
            print(f"ملفات Splash/Launch: {splash_files}")
            
            # تحليل AndroidManifest.xml
            if 'AndroidManifest.xml' in file_list:
                print("\n" + "="*50)
                print("تحليل AndroidManifest.xml...")
                
                # استخراج AndroidManifest.xml
                manifest_content = zip_ref.read('AndroidManifest.xml')
                
                # حفظ الملف مؤقتاً
                with open('temp_manifest.xml', 'wb') as f:
                    f.write(manifest_content)
                
                # تحليل الملف
                strings = read_binary_xml('temp_manifest.xml')
                
                # البحث عن أنشطة مهمة
                important_activities = []
                for string in strings:
                    if any(keyword in string.lower() for keyword in ['main', 'launcher', 'splash', 'start', 'welcome', 'activity']):
                        important_activities.append(string)
                
                print(f"\nالأنشطة المهمة المحتملة:")
                for activity in important_activities:
                    print(f"- {activity}")
                
                # تنظيف الملف المؤقت
                if os.path.exists('temp_manifest.xml'):
                    os.remove('temp_manifest.xml')
            
    except Exception as e:
        print(f"خطأ في تحليل APK: {e}")

if __name__ == "__main__":
    analyze_apk_structure()
