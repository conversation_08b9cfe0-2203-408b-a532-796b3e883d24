#!/usr/bin/env python3
import zipfile
import os
import sys

def extract_apk(apk_path, output_dir):
    """Extract APK file contents"""
    try:
        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Extract APK (which is essentially a ZIP file)
        with zipfile.ZipFile(apk_path, 'r') as zip_ref:
            zip_ref.extractall(output_dir)
        
        print(f"APK extracted successfully to {output_dir}")
        
        # List extracted contents
        print("\nExtracted contents:")
        for root, dirs, files in os.walk(output_dir):
            level = root.replace(output_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files[:10]:  # Show first 10 files only
                print(f"{subindent}{file}")
            if len(files) > 10:
                print(f"{subindent}... and {len(files) - 10} more files")
        
        return True
    except Exception as e:
        print(f"Error extracting APK: {e}")
        return False

if __name__ == "__main__":
    apk_file = "capcut-14-7-0.apk"
    output_directory = "capcut_extracted"
    
    if os.path.exists(apk_file):
        extract_apk(apk_file, output_directory)
    else:
        print(f"APK file {apk_file} not found!")
