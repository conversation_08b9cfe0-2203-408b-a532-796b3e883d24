"use strict";(()=>{var ce=Object.create;var W=Object.defineProperty;var fe=Object.getOwnPropertyDescriptor;var ue=Object.getOwnPropertyNames;var pe=Object.getPrototypeOf,he=Object.prototype.hasOwnProperty;var B=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),me=(e,t)=>{for(var o in t)W(e,o,{get:t[o],enumerable:!0})},ge=(e,t,o,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of ue(t))!he.call(e,n)&&n!==o&&W(e,n,{get:()=>t[n],enumerable:!(r=fe(t,n))||r.enumerable});return e};var de=(e,t,o)=>(o=e!=null?ce(pe(e)):{},ge(t||!e||!e.__esModule?W(o,"default",{value:e,enumerable:!0}):o,e));var z=B((Zt,K)=>{"use strict";K.exports=function(t,o){if(o=o.split(":")[0],t=+t,!t)return!1;switch(o){case"http":case"ws":return t!==80;case"https":case"wss":return t!==443;case"ftp":return t!==21;case"gopher":return t!==70;case"file":return!1}return t!==0}});var Z=B($=>{"use strict";var qe=Object.prototype.hasOwnProperty,$e;function Y(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(t){return null}}function J(e){try{return encodeURIComponent(e)}catch(t){return null}}function Fe(e){for(var t=/([^=?#&]+)=?([^&]*)/g,o={},r;r=t.exec(e);){var n=Y(r[1]),a=Y(r[2]);n===null||a===null||n in o||(o[n]=a)}return o}function Ve(e,t){t=t||"";var o=[],r,n;typeof t!="string"&&(t="?");for(n in e)if(qe.call(e,n)){if(r=e[n],!r&&(r===null||r===$e||isNaN(r))&&(r=""),n=J(n),r=J(r),n===null||r===null)continue;o.push(n+"="+r)}return o.length?t+o.join("&"):""}$.stringify=Ve;$.parse=Fe});var ie=B((Xt,ne)=>{"use strict";var X=z(),G=Z(),He=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,ee=/[\n\r\t]/g,Ke=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,te=/:\d+$/,ze=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,Ye=/^[a-zA-Z]:/;function V(e){return(e||"").toString().replace(He,"")}var F=[["#","hash"],["?","query"],function(t,o){return y(o.protocol)?t.replace(/\\/g,"/"):t},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],Q={hash:1,query:1};function re(e){var t;typeof window!="undefined"?t=window:typeof global!="undefined"?t=global:typeof self!="undefined"?t=self:t={};var o=t.location||{};e=e||o;var r={},n=typeof e,a;if(e.protocol==="blob:")r=new E(unescape(e.pathname),{});else if(n==="string"){r=new E(e,{});for(a in Q)delete r[a]}else if(n==="object"){for(a in e)a in Q||(r[a]=e[a]);r.slashes===void 0&&(r.slashes=Ke.test(e.href))}return r}function y(e){return e==="file:"||e==="ftp:"||e==="http:"||e==="https:"||e==="ws:"||e==="wss:"}function oe(e,t){e=V(e),e=e.replace(ee,""),t=t||{};var o=ze.exec(e),r=o[1]?o[1].toLowerCase():"",n=!!o[2],a=!!o[3],f=0,c;return n?a?(c=o[2]+o[3]+o[4],f=o[2].length+o[3].length):(c=o[2]+o[4],f=o[2].length):a?(c=o[3]+o[4],f=o[3].length):c=o[4],r==="file:"?f>=2&&(c=c.slice(2)):y(r)?c=o[4]:r?n&&(c=c.slice(2)):f>=2&&y(t.protocol)&&(c=o[4]),{protocol:r,slashes:n||y(r),slashesCount:f,rest:c}}function Je(e,t){if(e==="")return t;for(var o=(t||"/").split("/").slice(0,-1).concat(e.split("/")),r=o.length,n=o[r-1],a=!1,f=0;r--;)o[r]==="."?o.splice(r,1):o[r]===".."?(o.splice(r,1),f++):f&&(r===0&&(a=!0),o.splice(r,1),f--);return a&&o.unshift(""),(n==="."||n==="..")&&o.push(""),o.join("/")}function E(e,t,o){if(e=V(e),e=e.replace(ee,""),!(this instanceof E))return new E(e,t,o);var r,n,a,f,c,p,m=F.slice(),d=typeof t,i=this,h=0;for(d!=="object"&&d!=="string"&&(o=t,t=null),o&&typeof o!="function"&&(o=G.parse),t=re(t),n=oe(e||"",t),r=!n.protocol&&!n.slashes,i.slashes=n.slashes||r&&t.slashes,i.protocol=n.protocol||t.protocol||"",e=n.rest,(n.protocol==="file:"&&(n.slashesCount!==2||Ye.test(e))||!n.slashes&&(n.protocol||n.slashesCount<2||!y(i.protocol)))&&(m[3]=[/(.*)/,"pathname"]);h<m.length;h++){if(f=m[h],typeof f=="function"){e=f(e,i);continue}a=f[0],p=f[1],a!==a?i[p]=e:typeof a=="string"?(c=a==="@"?e.lastIndexOf(a):e.indexOf(a),~c&&(typeof f[2]=="number"?(i[p]=e.slice(0,c),e=e.slice(c+f[2])):(i[p]=e.slice(c),e=e.slice(0,c)))):(c=a.exec(e))&&(i[p]=c[1],e=e.slice(0,c.index)),i[p]=i[p]||r&&f[3]&&t[p]||"",f[4]&&(i[p]=i[p].toLowerCase())}o&&(i.query=o(i.query)),r&&t.slashes&&i.pathname.charAt(0)!=="/"&&(i.pathname!==""||t.pathname!=="")&&(i.pathname=Je(i.pathname,t.pathname)),i.pathname.charAt(0)!=="/"&&y(i.protocol)&&(i.pathname="/"+i.pathname),X(i.port,i.protocol)||(i.host=i.hostname,i.port=""),i.username=i.password="",i.auth&&(c=i.auth.indexOf(":"),~c?(i.username=i.auth.slice(0,c),i.username=encodeURIComponent(decodeURIComponent(i.username)),i.password=i.auth.slice(c+1),i.password=encodeURIComponent(decodeURIComponent(i.password))):i.username=encodeURIComponent(decodeURIComponent(i.auth)),i.auth=i.password?i.username+":"+i.password:i.username),i.origin=i.protocol!=="file:"&&y(i.protocol)&&i.host?i.protocol+"//"+i.host:"null",i.href=i.toString()}function Ze(e,t,o){var r=this;switch(e){case"query":typeof t=="string"&&t.length&&(t=(o||G.parse)(t)),r[e]=t;break;case"port":r[e]=t,X(t,r.protocol)?t&&(r.host=r.hostname+":"+t):(r.host=r.hostname,r[e]="");break;case"hostname":r[e]=t,r.port&&(t+=":"+r.port),r.host=t;break;case"host":r[e]=t,te.test(t)?(t=t.split(":"),r.port=t.pop(),r.hostname=t.join(":")):(r.hostname=t,r.port="");break;case"protocol":r.protocol=t.toLowerCase(),r.slashes=!o;break;case"pathname":case"hash":if(t){var n=e==="pathname"?"/":"#";r[e]=t.charAt(0)!==n?n+t:t}else r[e]=t;break;case"username":case"password":r[e]=encodeURIComponent(t);break;case"auth":var a=t.indexOf(":");~a?(r.username=t.slice(0,a),r.username=encodeURIComponent(decodeURIComponent(r.username)),r.password=t.slice(a+1),r.password=encodeURIComponent(decodeURIComponent(r.password))):r.username=encodeURIComponent(decodeURIComponent(t))}for(var f=0;f<F.length;f++){var c=F[f];c[4]&&(r[c[1]]=r[c[1]].toLowerCase())}return r.auth=r.password?r.username+":"+r.password:r.username,r.origin=r.protocol!=="file:"&&y(r.protocol)&&r.host?r.protocol+"//"+r.host:"null",r.href=r.toString(),r}function Qe(e){(!e||typeof e!="function")&&(e=G.stringify);var t,o=this,r=o.host,n=o.protocol;n&&n.charAt(n.length-1)!==":"&&(n+=":");var a=n+(o.protocol&&o.slashes||y(o.protocol)?"//":"");return o.username?(a+=o.username,o.password&&(a+=":"+o.password),a+="@"):o.password?(a+=":"+o.password,a+="@"):o.protocol!=="file:"&&y(o.protocol)&&!r&&o.pathname!=="/"&&(a+="@"),(r[r.length-1]===":"||te.test(o.hostname)&&!o.port)&&(r+=":"),a+=r+o.pathname,t=typeof o.query=="object"?e(o.query):o.query,t&&(a+=t.charAt(0)!=="?"?"?"+t:t),o.hash&&(a+=o.hash),a}E.prototype={set:Ze,toString:Qe};E.extractProtocol=oe;E.location=re;E.trimLeft=V;E.qs=G;ne.exports=E});var s=globalThis;var D=class{constructor(){this.map={};this.flag=1}getSelfPropagationId(t){let o=this.flag;return this.map[o]=t,this.flag++,o}getTimeoutObject(t){return this.map[t]}},S=new D;s.__PIA_NATIVE__||(s.__PIA_NATIVE__={});function _e(){let e={postMessage:t=>{typeof t=="object"&&s.NativeModules.get("BaseModule").postBridgeMessage(t)}};s.pia_bridge=e,s.__PIA_NATIVE__.onWorkerBridgeMessage=()=>{let t=s.NativeModules.get("BaseModule").getBridgeMessage();s.pia_bridge.onmessage&&s.pia_bridge.onmessage(t)}}_e();function be(){s.close=s.NativeModules.get("BaseModule").terminate}be();var k={};me(k,{ClientMode:()=>Ge,FeaturesSetExport:()=>Me,LOGGER_LEVEL_METHOD:()=>Oe,LOGGER_PARENT_TYPE_COLOR:()=>ye,LOGGER_PARENT_TYPE_NAME:()=>ve,LOGGER_TYPE_COLOR:()=>Ie,LOGGER_TYPE_NAME:()=>xe,RUNTIME_EVENT_4_RENDER_NAME:()=>Ae,RUNTIME_EVENT_4_WORKER_NAME:()=>Le,RuntimeDiagnostic:()=>Se,RuntimeDiagnosticMessage:()=>ke,RuntimeEvent4Render:()=>Ne,RuntimeEvent4Worker:()=>Ce,RuntimeLogLevel:()=>we,RuntimeLogParentType:()=>Re,RuntimeLogType:()=>Ee,RuntimePrefetchResponseStatusCode:()=>Pe,WorkerBootstrapCode:()=>Te});var A,C,L,P,T,we={DEBUG:0,LOG:1,WARN:2,ERROR:3},Re={Render:0,Worker:1},ve=(A={},A[0]="Render",A[1]="Worker",A),ye=(C={},C[0]="color:#5c6ac4;font-weight:bold;",C[1]="color:#e89015;font-weight:bold;",C),Ee={Runtime:0,User:1},xe=(L={},L[0]="PIA Runtime",L[1]="User",L),Ie=(P={},P[0]="color:#a626a4;",P[1]="color:#50a14f;",P),Se={PREFETCH_NOT_CONSUME:0,WORKER_ERROR:1},ke={FEATURES_NOT_YET_REGISTERED:"PIA runtime features not yet registered."},Oe=(T={},T[1]="log",T[2]="warn",T[3]="error",T[0]="debug",T);var Te={SUCCESS:0,TIMEOUT:1,WORKER_NOT_AVAILABLE:2,WORKER_ID_NOT_MATCH:3,WORKER_BOOT_FAILED:4,MANIFEST_VERSION_NOT_MATCH:5,STREAMING_NSR_TIMEOUT:6};var Me={Prefetch:0,InternalLogger:1};var q,M,Ne={onConnect:10},Ae=(q={},q[10]="Connect to Worker",q),Ce={Native$onPostMessageError:1,onWorkerReady:10,onWorkerPrefetchReady:11,onWorkerLog:12},Le=(M={},M[1]="Native PostMessage Error",M[10]="Worker Ready",M[11]="Worker Prefetch Ready",M),Pe={SUCCESS:1,FAIL:-2,TIMEOUT:-4,ABORTED:-7,NOT_EXISTS:-1002,UNREGISTERED_ID:-1004};var Ge={InSDK:"in-sdk",OutSDK:"out-sdk",Rendering:"rendering",InSDK2OutSDK:"in-sdk-2-out-sdk"};function Ue(e){let t=(o,r)=>`000${o}`.slice(-r);return`${t(e.getHours(),2)}:${t(e.getMinutes(),2)}:${t(e.getSeconds(),2)}.${t(e.getMilliseconds(),3)}`}function je(e){let t=new Set,o=JSON.stringify(e,(r,n)=>{if(typeof n=="function")return`[Function: ${n.name||"anonymous"}]`;if(typeof n=="undefined")return"undefined";if(n instanceof Error)return`Error: ${n.message}${n.stack?`
${n.stack}`:""}`;if(n instanceof Promise)return"Promise {<unknown>}";if(n&&typeof n=="object"){if(t.has(n))return"[Circular]";t.add(n)}return n});return t.clear(),o}function We(e){return e.map(je).join(" ")}function Be(){function e(o){return(...r)=>{let n=`[${Ue(new Date)}] ${We(r)}`;s.NativeModules.get("BaseModule").log(n,o)}}let t=e(k.RuntimeLogLevel.LOG);s.console={log:t,info:t,error:e(k.RuntimeLogLevel.ERROR),debug:e(k.RuntimeLogLevel.DEBUG),warn:e(k.RuntimeLogLevel.WARN)}}Be();function De(){let e=s.NativeModules.get("BaseModule").getGlobalProps(),{businessProps:t}=e;s.globalProps=t}De();var le=de(ie());var U={};try{(function(e,t){if(new e("q=%2B").get("q")!==t||new e({q:t}).get("q")!==t||new e([["q",t]]).get("q")!==t||new e(`q=
`).toString()!=="q=%0A"||new e({q:" &"}).toString()!=="q=+%26"||new e({q:"%zx"}).toString()!=="q=%25zx")throw e;U.URLSearchParams=e})(URLSearchParams,"+")}catch(e){(function(t,o,r){"use strict";var n=t.create,a=t.defineProperty,f=/[!'\(\)~]|%20|%00/g,c=/%(?![0-9a-fA-F]{2})/g,p=/\+/g,m={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"},d={append:function(l,g){R(this._ungap,l,g)},delete:function(l){delete this._ungap[l]},get:function(l){return this.has(l)?this._ungap[l][0]:null},getAll:function(l){return this.has(l)?this._ungap[l].slice(0):[]},has:function(l){return l in this._ungap},set:function(l,g){this._ungap[l]=[o(g)]},forEach:function(l,g){var w=this;for(var _ in w._ungap)w._ungap[_].forEach(I,_);function I(v){l.call(g,v,o(_),w)}},toJSON:function(){return{}},toString:function(){var l=[];for(var g in this._ungap)for(var w=b(g),_=0,I=this._ungap[g];_<I.length;_++)l.push(w+"="+b(I[_]));return l.join("&")}};for(var i in d)a(h.prototype,i,{configurable:!0,writable:!0,value:d[i]});U.URLSearchParams=h;function h(l){var g=n(null);switch(a(this,"_ungap",{value:g}),!0){case!l:break;case typeof l=="string":l.charAt(0)==="?"&&(l=l.slice(1));for(var w=l.split("&"),_=0,I=w.length;_<I;_++){var v=w[_],j=v.indexOf("=");-1<j?R(g,u(v.slice(0,j)),u(v.slice(j+1))):v.length&&R(g,u(v),"")}break;case r(l):for(var _=0,I=l.length;_<I;_++){var v=l[_];R(g,v[0],v[1])}break;case"forEach"in l:l.forEach(x,g);break;default:for(var H in l)R(g,H,l[H])}}function x(l,g){R(this,g,l)}function R(l,g,w){var _=r(w)?w.join(","):w;g in l?l[g].push(_):l[g]=[_]}function u(l){return decodeURIComponent(l.replace(c,"%25").replace(p," "))}function b(l){return encodeURIComponent(l).replace(f,O)}function O(l){return m[l]}})(Object,String,Array.isArray)}(function(e){var t=!1;try{t=!!Symbol.iterator}catch(r){}"forEach"in e||(e.forEach=function(n,a){var f=this,c=Object.create(null);this.toString().replace(/=[\s\S]*?(?:&|$)/g,"=").split("=").forEach(function(p){!p.length||p in c||(c[p]=f.getAll(p)).forEach(function(m){n.call(a,m,p,f)})})}),"keys"in e||(e.keys=function(){return o(this,function(n,a){this.push(a)})}),"values"in e||(e.values=function(){return o(this,function(n,a){this.push(n)})}),"entries"in e||(e.entries=function(){return o(this,function(n,a){this.push([a,n])})}),t&&!(Symbol.iterator in e)&&(e[Symbol.iterator]=e.entries),"sort"in e||(e.sort=function(){for(var n=this.entries(),a=n.next(),f=a.done,c=[],p=Object.create(null),m,d,i;!f;)i=a.value,d=i[0],c.push(d),d in p||(p[d]=[]),p[d].push(i[1]),a=n.next(),f=a.done;for(c.sort(),m=0;m<c.length;m++)this.delete(c[m]);for(m=0;m<c.length;m++)d=c[m],this.append(d,p[d].shift())});function o(r,n){var a=[];return r.forEach(n,a),t?a[Symbol.iterator]():{next:function(){var f=a.shift();return{done:f===void 0,value:f}}}}(function(r){var n=r.defineProperty,a=r.getOwnPropertyDescriptor,f=function(i){function h(u,b){e.append.call(this,u,b),u=this.toString(),i.set.call(this._usp,u?"?"+u:"")}function x(u){e.delete.call(this,u),u=this.toString(),i.set.call(this._usp,u?"?"+u:"")}function R(u,b){e.set.call(this,u,b),u=this.toString(),i.set.call(this._usp,u?"?"+u:"")}return function(u,b){return u.append=h,u.delete=x,u.set=R,n(u,"_usp",{configurable:!0,writable:!0,value:b})}},c=function(i){return function(h,x){return n(h,"_searchParams",{configurable:!0,writable:!0,value:i(x,h)}),x}},p=function(i){var h=i.append;i.append=e.append,URLSearchParams.call(i,i._usp.search.slice(1)),i.append=h},m=function(i,h){if(!(i instanceof h))throw new TypeError("'searchParams' accessed on an object that does not implement interface "+h.name)},d=function(i){var h=i.prototype,x=a(h,"searchParams"),R=a(h,"href"),u=a(h,"search"),b;!x&&u&&u.set&&(b=c(f(u)),r.defineProperties(h,{href:{get:function(){return R.get.call(this)},set:function(O){var l=this._searchParams;R.set.call(this,O),l&&p(l)}},search:{get:function(){return u.get.call(this)},set:function(O){var l=this._searchParams;u.set.call(this,O),l&&p(l)}},searchParams:{get:function(){return m(this,i),this._searchParams||b(this,new URLSearchParams(this.search.slice(1)))},set:function(O){m(this,i),b(this,O)}}}))};try{d(HTMLAnchorElement),/^function|object$/.test(typeof URL)&&URL.prototype&&d(URL)}catch(i){}})(Object)})(U.URLSearchParams.prototype,Object);var se=U.URLSearchParams;function ae(){s.URLSearchParams=se}function Xe(){ae(),s.URL=class{constructor(t,o){let{hash:r,host:n,hostname:a,origin:f,username:c,password:p,pathname:m,port:d,protocol:i,query:h}=new le.default(t,o,!1);this.hash=r,this.host=n,this.hostname=a,this.href=t,this.origin=f,this.password=p,this.pathname=m,this.username=c,this.port=d,this.protocol=i,this.search=h,Object.defineProperty(this,"searchParams",{value:new URLSearchParams(this.search),writable:!1})}}}Xe();function et(){let e=s.NativeModules.get("BaseModule").getHref();s.location=new s.URL(e)}et();var N=[];s._onmessage={handler:void 0,messageQueue:[],flush:e=>{s._onmessage.handler?s._onmessage.handler(e):N.push(e)},setHandler:e=>{setTimeout(()=>{if(typeof e=="function"&&N.length>0){for(let t=0;t<N.length;t++){let o=N[t];e(o)}N.length=0}},0),s._onmessage.handler=e}};Object.defineProperty(s,"onmessage",{get(){return s._onmessage.flush},set(e){s._onmessage.setHandler(e)}});function tt(){s.postMessage=s.NativeModules.get("BaseModule").postMessage}function rt(){s.__PIA_NATIVE__.onWorkerMessage=()=>{let e=s.NativeModules.get("BaseModule").getMessage();s.onmessage(e)}}tt();rt();function ot(){let e=s.NativeModules.get("BaseModule").getUserAgent();s.navigator={userAgent:e}}ot();function nt(){s._setInterval=s.setInterval,s.setInterval=(e,t,...o)=>{let r=s._setInterval(()=>{e(...o)},t);return S.getSelfPropagationId(r)}}nt();function it(){s._setTimeout=s.setTimeout,s.setTimeout=(e,t,...o)=>{let r=s._setTimeout(()=>{e(...o)},t);return S.getSelfPropagationId(r)}}it();function st(){let e=s.NativeModules.get("BaseModule").getWorkerName();s.name=e}st();function at(){s._clearInterval=s.clearInterval,s.clearInterval=e=>{let t=S.getTimeoutObject(e);s._clearInterval(t)}}at();function lt(){s._clearTimeout=s.clearTimeout,s.clearTimeout=e=>{let t=S.getTimeoutObject(e);s._clearTimeout(t)}}lt();function ct(){s.importScriptsAsync=function(e){return new Promise((t,o)=>{s.NativeModules.get("BaseModule").importScriptsAsync(e,t,r=>o(r))})}}ct();function ft(){s.canIUse=e=>{switch(e){case"log2":return!0;default:return!1}}}ft();function ut(){s.__PIA_NATIVE__.traceMark=e=>{s.NativeModules.get("BaseModule").trace(JSON.stringify(e))}}ut();})();
