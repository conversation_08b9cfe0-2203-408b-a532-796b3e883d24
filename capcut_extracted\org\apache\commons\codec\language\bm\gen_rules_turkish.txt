/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"ç" "" "" "tS"
"ğ" "" "" "" // to show that previous vowel is long
"ş" "" "" "S"
"ü" "" "" "Q"
"ö" "" "" "Y"
"ı" "" "" "(e|i|)" // as "e" in English "label"

"a" "" "" "a"
"b" "" "" "b"
"c" "" "" "dZ"
"d" "" "" "d"
"e" "" "" "e"
"f" "" "" "f"
"g" "" "" "g"
"h" "" "" "h"
"i" "" "" "i"
"j" "" "" "Z"
"k" "" "" "k"
"l" "" "" "l"
"m" "" "" "m"
"n" "" "" "n"
"o" "" "" "o"
"p" "" "" "p"
"q" "" "" "k" // foreign words
"r" "" "" "r"
"s" "" "" "s"
"t" "" "" "t"
"u" "" "" "u"
"v" "" "" "v"
"w" "" "" "v" // foreign words
"x" "" "" "ks" // foreign words
"y" "" "" "j"
"z" "" "" "z" 
